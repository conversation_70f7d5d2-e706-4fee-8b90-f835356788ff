import { Metadata } from "next";
import { EMAIL_CONTACT, WEBNA<PERSON> } from "@/lib/constants";
import Markdown from "@/components/ui/custom/markdown-react";

export const dynamic = "force-static";

export const metadata: Metadata = {
	title: `Privacy Policy | ${WEBNAME}`,
	description:
		"Read the Privacy Policy to understand how we collect, use, and protect your personal information. Your data privacy and security are our top priorities.",
	alternates: {
		canonical: "/privacy-policy",
	},
};

export default function Page() {
	return (
		<article className="container max-w-4xl px-6 py-12">
			<div className="text-zinc-800">
				<Markdown>{content}</Markdown>
			</div>
		</article>
	);
}

const content = `
# Privacy Policy

Last Updated: July 16, 2025

Our image cropping website is designed to respect your privacy. Below is our Privacy Policy outlining how we handle your data:

## Data Collection
- **No Personal Data Collected**: We do not collect, store, or process any personal information or images you upload for cropping.
- **Temporary Processing**: Images are processed in your browser and are not stored on our servers. Once you close the browser or refresh the page, all data is deleted.

## Data Usage
- **No Storage**: We do not save, store, or share any images or data you upload.

## Third Parties
- **No Third-Party Sharing**: Your images and data are not shared with any third parties.

## Security
- **In-Browser Processing**: All image cropping happens locally in your browser, ensuring your data remains secure and private.

## Changes to This Policy
We may update this Privacy Policy from time to time. Any changes will be posted on this page with an updated "Last Updated" date.

## Contact Us
If you have any questions about this Privacy Policy, please contact us at [${EMAIL_CONTACT}](mailto:${EMAIL_CONTACT}).
`;
